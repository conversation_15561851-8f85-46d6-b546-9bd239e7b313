# Pipeline Testing Web Application

A modern Java web application that demonstrates pipeline testing concepts with dynamic visual effects and glowing animations. Built using Java Servlets, JSP, and Maven, this application generates a WAR (Web Application Archive) file for deployment to servlet containers.

## 🚀 Quick Start

### Prerequisites

- **Java JDK** >= 11 (OpenJDK or Oracle JDK)
- **Maven** >= 3.6.0
- **Apache Tomcat** >= 9.0 (or another servlet container)

### Installation & Development

1. **Clone or navigate to the project directory:**
   ```bash
   cd pipeline-testing-app
   ```

2. **Compile the application:**
   ```bash
   mvn clean compile
   ```

3. **Run tests:**
   ```bash
   mvn test
   ```

4. **Build the WAR file:**
   ```bash
   mvn package
   ```

5. **Deploy to Tomcat:**
   - Copy `target/pipeline-testing-app.war` to your Tomcat `webapps/` directory
   - Start Tomcat server
   - Access the application at: `http://localhost:8080/pipeline-testing-app/`

## 📦 Available Maven Commands

### Development & Building
- `mvn clean` - Clean the target directory
- `mvn compile` - Compile the Java source code     
- `mvn test` - Run unit tests
- `mvn package` - Create the WAR file
- `mvn clean package` - Clean and build the WAR file

### Quality Assurance
- `mvn test` - Run all unit tests
- `mvn surefire-report:report` - Generate test reports
- `mvn site` - Generate project documentation

### Deployment Preparation
- `mvn clean package` - Build production-ready WAR file
- `mvn verify` - Run integration tests (if configured)

### Utilities
- `mvn clean` - Remove target directory and build artifacts
- `mvn dependency:tree` - Display project dependencies
- `mvn help:effective-pom` - Show the effective POM configuration

## 🏗️ Build Process

### Development to Production Workflow

1. **Development Phase:**
   ```bash
   mvn clean compile
   ```
   - Compiles Java source code
   - Validates servlet and JSP syntax
   - Prepares classes for testing

2. **Quality Checks:**
   ```bash
   mvn test
   ```
   - Runs JUnit unit tests
   - Validates servlet functionality
   - Generates test reports in `target/surefire-reports/`

3. **Production Build:**
   ```bash
   mvn clean package
   ```
   - Compiles all source code
   - Runs all tests
   - Creates WAR file in `target/` directory
   - Includes all web resources (CSS, JS, JSP files)
   - Optimizes for deployment

4. **Deployment Verification:**
   ```bash
   # Deploy WAR to Tomcat and verify
   cp target/pipeline-testing-app.war $TOMCAT_HOME/webapps/
   # Start Tomcat and test application
   ```
   - Deploy WAR file to servlet container
   - Verify all endpoints work correctly
   - Check application functionality


## 📁 Project Structure

```
pipeline-testing-app/
├── src/
│   ├── main/
│   │   ├── java/
│   │   │   └── com/pipelinetesting/servlet/
│   │   │       └── PipelineTestingServlet.java    # Main servlet
│   │   ├── webapp/
│   │   │   ├── WEB-INF/
│   │   │   │   ├── web.xml                        # Deployment descriptor
│   │   │   │   └── error/
│   │   │   │       ├── 404.jsp                    # 404 error page
│   │   │   │       └── 500.jsp                    # 500 error page
│   │   │   ├── css/
│   │   │   │   └── app.css                        # Application styles
│   │   │   ├── js/
│   │   │   │   └── app.js                         # JavaScript functionality
│   │   │   └── index.jsp                          # Main JSP page
│   │   └── resources/                             # Application resources
│   └── test/
│       └── java/
│           └── com/pipelinetesting/servlet/
│               └── PipelineTestingServletTest.java # Unit tests
├── target/                                        # Build output (generated)
│   └── pipeline-testing-app.war                  # Generated WAR file
├── pom.xml                                        # Maven configuration
├── bitbucket-pipelines.yml                       # CI/CD pipeline
└── README.md                                      # This file
```


## 🌐 Deployment

The built WAR file can be deployed to any Java servlet container:

### Configuring Tomcat Server

Before deploying your application, you need to install and configure Apache Tomcat and its prerequisites:

#### Prerequisites Installation

First, install Java and Maven which are required for both Tomcat and the application:

```bash
# Update package lists
sudo apt-get update

# Install Java 11
sudo apt-get install -y openjdk-11-jdk

# Verify Java installation
java -version

# Install Maven
sudo apt-get install -y maven

# Verify Maven installation
mvn -version

# Install other useful tools
sudo apt-get install -y wget unzip curl
```

#### Installing Tomcat on EC2 Instance

```bash
# Create a directory for Tomcat
sudo mkdir -p /opt/tomcat

# Download Tomcat (adjust version as needed)
wget https://dlcdn.apache.org/tomcat/tomcat-9/v9.0.106/bin/apache-tomcat-9.0.106.tar.gz

# Extract the archive
sudo tar -xzf https://dlcdn.apache.org/tomcat/tomcat-9/v9.0.106/bin/apache-tomcat-9.0.106.tar.gz

# Rename the directory
mv apache-tomcat-9.0.106 tomcat

# Set proper permissions
sudo chmod +x /home/<USER>/tomcat/bin/*.sh

# Create a Tomcat user (optional)
sudo useradd -r tomcat
sudo chown -R tomcat:tomcat /opt/tomcat
```

#### Configuring Tomcat

1. **Set JAVA_HOME environment variable**:
   
   On Linux:
   ```bash
   echo 'export JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64' >> ~/.bashrc
   echo 'export CATALINA_HOME=/home/<USER>/tomcat' >> ~/.bashrc
   source ~/.bashrc
   ```

2. **Configure Tomcat users** (for admin access):
   
   Edit `$CATALINA_HOME/conf/tomcat-users.xml`:
   ```xml
   <tomcat-users>
     <role rolename="manager-gui"/>
     <role rolename="manager-script"/>
     <role rolename="admin-gui"/>
     <user username="admin" password="your_secure_password" roles="manager-gui,admin-gui,manager-script"/>
   </tomcat-users>
   ```

3. **Configure server ports** (optional):
   
   Edit `$CATALINA_HOME/conf/server.xml` to change default ports if needed:
   ```xml
   <!-- HTTP Connector on port 8080 -->
   <Connector port="8080" protocol="HTTP/1.1"
              connectionTimeout="20000"
              redirectPort="8443" />
   ```

4. **Starting and stopping Tomcat**:
   
   On Linux:
   ```bash
   # Start Tomcat
   $CATALINA_HOME/bin/startup.sh
   
   # Stop Tomcat
   $CATALINA_HOME/bin/shutdown.sh
   ```

5. **Configure Tomcat to start automatically on system reboot**:
   
   ```bash
   # Create a systemd service file for Tomcat
   sudo vim /etc/systemd/system/tomcat.service
   ```
   
   Add the following content to the file:
   ```
   [Unit]
   Description=Apache Tomcat Web Application Container
   After=network.target
   
   [Service]
   Type=forking
   
   Environment=JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
   Environment=CATALINA_HOME=/home/<USER>/tomcat
   Environment=CATALINA_BASE=/home/<USER>/tomcat
   
   ExecStart=/home/<USER>/tomcat/bin/startup.sh
   ExecStop=/home/<USER>/tomcat/bin/shutdown.sh
   
   UMask=0007
   RestartSec=10
   Restart=always
   
   [Install]
   WantedBy=multi-user.target
   ```
   
   Enable and start the service:
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl enable tomcat
   sudo systemctl start tomcat
   
   # Check status
   sudo systemctl status tomcat
   ```

6. **Verify Tomcat is running**:
   
   Open a browser and navigate to: `http://localhost:8080`
   
   You should see the Tomcat welcome page.

#### Troubleshooting Tomcat

- **Port conflicts**: If port 8080 is already in use, change the port in `server.xml`
- **Memory issues**: Adjust JVM memory settings in `catalina.sh` or `catalina.bat`
- **Permission problems**: Ensure proper file permissions on Linux systems
- **Logs**: Check `$CATALINA_HOME/logs/catalina.out` for error messages

### Build for Deployment
```bash
mvn clean package
```

This creates a production-ready WAR file in the `target/` directory with:
- Compiled Java classes
- Web resources (JSP, CSS, JavaScript)
- Deployment descriptor (web.xml)
- All required dependencies
- Optimized for servlet container deployment

### Tomcat Deployment Example
```bash
# Build the WAR file
mvn clean package

# Copy to Tomcat webapps directory
cp target/pipeline-testing-app.war $CATALINA_HOME/webapps/

# Start Tomcat (if not running)
$CATALINA_HOME/bin/startup.sh

# Access application
# http://localhost:8080/pipeline-testing-app/
```


## 🎯 Pipeline Testing Concepts

This application demonstrates key pipeline testing concepts for a Java application:

1. **Automated Builds**: The pom.xml is configured to build the project, run tests, and package it with a single   command (mvn package).
2. **Unit Testing**: Automated execution of JUnit tests to validate business logic before deployment.
3. **Static Analysis**:  Code analysis tools like Checkstyle or PMD can be integrated into the Maven lifecycle to enforce code quality.
4. **Artifact Management**: The pipeline produces a versioned .war file, a standard artifact for Java web applications.
5. **Automated Deployment**: The CI/CD pipeline can be scripted to automatically deploy the WAR file to a staging or production Tomcat server.



## 🔧 Self-Hosted Runner Setup

### Prerequisites for Linux Shell Runner

Before using the self-hosted runner for your pipeline, ensure the following prerequisites are installed:

```bash
# Update system packages
sudo apt-get update -y

# Install OpenJDK (required for Maven and Java compilation)
sudo apt-get install -y openjdk-11-jdk

# Install Maven (for building the Java web application)
sudo apt-get install -y maven

# Install zip utility (for Bitbucket Downloads)
sudo apt-get install -y zip

# Verify installations
java -version
mvn -version
zip --version
```

These prerequisites are essential for running the Java web application pipeline tasks in a self-hosted Linux shell environment:
- **OpenJDK 11+**: Required for compiling Java source code and running Maven
- **Maven 3.6+**: Required for dependency management and building the WAR file
- **zip**: Required for creating deployment archives

The `-y` flag automatically confirms installation prompts.

### Configuring Self-Hosted Runner in bitbucket-pipelines.yml

To use a self-hosted runner in your pipeline, add the following configuration to your `bitbucket-pipelines.yml` file:

```yaml
defaults: &self_hosted_runner
  runs-on:
    - self.hosted
    - linux.shell

pipelines:
  default:
    - step:
        <<: *self_hosted_runner
        name: Your Step Name
        script:
          - your commands here
```

This configuration tells Bitbucket Pipelines to use your self-hosted runner with Linux shell capabilities instead of Bitbucket's default runners. The YAML anchor `&self_hosted_runner` allows you to reuse this configuration across multiple steps.

