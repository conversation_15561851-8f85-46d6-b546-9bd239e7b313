package com.pipelinetesting.servlet;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Main servlet for the Pipeline Testing Web Application.
 * Handles requests and serves the main page with pipeline testing content.
 * Configuration is handled via web.xml to avoid conflicts.
 */
public class PipelineTestingServlet extends HttpServlet {

    private static final long serialVersionUID = 1L;

    /**
     * Handles GET requests to display the main pipeline testing page.
     */
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Set response content type
        response.setContentType("text/html;charset=UTF-8");

        // Generate servlet-specific content
        PrintWriter out = response.getWriter();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        out.println("<!DOCTYPE html>");
        out.println("<html lang='en'>");
        out.println("<head>");
        out.println("<meta charset='UTF-8'>");
        out.println("<meta name='viewport' content='width=device-width, initial-scale=1.0'>");
        out.println("<title>Pipeline Testing Servlet - Java Web Application</title>");
        out.println("<style>");
        out.println("body { font-family: 'Segoe UI', sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: white; }");
        out.println(".container { max-width: 1200px; margin: 0 auto; padding: 20px; }");
        out.println(".title { text-align: center; font-size: 3rem; margin: 2rem 0; text-shadow: 0 0 20px rgba(255,255,255,0.5); }");
        out.println(".content { background: rgba(255,255,255,0.1); padding: 2rem; border-radius: 15px; backdrop-filter: blur(10px); margin: 2rem 0; }");
        out.println(".info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 2rem; }");
        out.println(".info-item { background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 8px; }");
        out.println(".nav-links { text-align: center; margin: 2rem 0; }");
        out.println(".nav-links a { color: white; text-decoration: none; margin: 0 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); border-radius: 5px; }");
        out.println(".nav-links a:hover { background: rgba(255,255,255,0.3); }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        out.println("<div class='container'>");
        out.println("<h1 class='title'>🚀 Pipeline Testing Servlet</h1>");
        out.println("<div class='nav-links'>");
        out.println("<a href='" + request.getContextPath() + "/'>Return to Home</a>");
        out.println("</div>");
        out.println("<div class='content'>");
        out.println("<h2>✅ Pipeline Testing Hub - Servlet Application</h2>");
        out.println("<p>Welcome to the Pipeline Testing Hub servlet interface. This demonstrates the core functionality of our Java web application with successful deployment and configuration.</p>");
        out.println("<div style='margin: 2rem 0; padding: 1.5rem; background: rgba(255,255,255,0.1); border-radius: 10px;'>");
        out.println("<h3>🚀 Application Status</h3>");
        out.println("<ul style='list-style: none; padding: 0;'>");
        out.println("<li style='margin: 0.5rem 0;'>✅ Java servlet compilation successful</li>");
        out.println("<li style='margin: 0.5rem 0;'>✅ WAR deployment completed</li>");
        out.println("<li style='margin: 0.5rem 0;'>✅ Servlet mapping configured</li>");
        out.println("<li style='margin: 0.5rem 0;'>✅ Application context loaded</li>");
        out.println("</ul>");
        out.println("</div>");
        out.println("<div class='info-grid'>");
        out.println("<div class='info-item'><strong>Java Version:</strong> " + System.getProperty("java.version") + "</div>");
        out.println("<div class='info-item'><strong>Server Info:</strong> " + getServletContext().getServerInfo() + "</div>");
        out.println("<div class='info-item'><strong>Context Path:</strong> " + request.getContextPath() + "</div>");
        out.println("<div class='info-item'><strong>Generated At:</strong> " + timestamp + "</div>");
        out.println("</div>");
        out.println("</div>");
        out.println("</div>");
        out.println("</body>");
        out.println("</html>");
    }

    /**
     * Handles POST requests (currently redirects to GET).
     */
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // For now, redirect POST requests to GET
        response.sendRedirect(request.getContextPath() + "/app");
    }

    /**
     * Returns information about this servlet.
     */
    @Override
    public String getServletInfo() {
        return "Pipeline Testing Servlet - Demonstrates pipeline testing concepts in a Java web application";
    }
}
