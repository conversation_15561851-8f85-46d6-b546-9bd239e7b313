<%@ page contentType="text/html;charset=UTF-8" language="java" isErrorPage="true" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>500 - Server Error | Pipeline Testing App</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/app.css">
    <style>
        .error-container {
            text-align: center;
            padding: 2rem;
        }
        .error-code {
            font-size: 6rem;
            font-weight: 900;
            color: #ff6b6b;
            margin: 0;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
        }
        .error-message {
            font-size: 1.5rem;
            color: #e0e0e0;
            margin: 1rem 0;
        }
        .error-description {
            font-size: 1rem;
            color: #b0b0b0;
            margin: 2rem 0;
            line-height: 1.6;
        }
        .back-link {
            display: inline-block;
            padding: 1rem 2rem;
            background: linear-gradient(45deg, #4ecdc4, #45b7d1);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }
        .back-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(78, 205, 196, 0.3);
        }
        .error-details {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            color: #d0d0d0;
        }
    </style>
</head>
<body>
    <div class="app">
        <div class="main-content">
            <div class="error-container">
                <h1 class="error-code">500</h1>
                <h2 class="error-message">Internal Server Error</h2>
                <div class="content-section">
                    <p class="error-description">
                        Something went wrong on our server while processing your request.
                        Our development team has been notified and is working to fix this issue.
                    </p>
                    
                    <% if (request.getAttribute("javax.servlet.error.message") != null) { %>
                        <div class="error-details">
                            <strong>Error Details:</strong><br>
                            <%= request.getAttribute("javax.servlet.error.message") %>
                        </div>
                    <% } %>
                    
                    <a href="${pageContext.request.contextPath}/" class="back-link">
                        Return to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
