<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

    <display-name>Pipeline Testing Web Application</display-name>
    <description>A Java web application demonstrating pipeline testing concepts with dynamic visual effects</description>

    <!-- Main Pipeline Testing Servlet -->
    <servlet>
        <servlet-name>PipelineTestingServlet</servlet-name>
        <servlet-class>com.pipelinetesting.servlet.PipelineTestingServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>PipelineTestingServlet</servlet-name>
        <url-pattern>/app</url-pattern>
    </servlet-mapping>

    <!-- Welcome file list -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
    </welcome-file-list>

    <!-- Session configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <!-- Error pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/WEB-INF/error/404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/WEB-INF/error/500.jsp</location>
    </error-page>

</web-app>
