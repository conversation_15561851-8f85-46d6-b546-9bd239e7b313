/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  line-height: 1.6;
}

/* App Container */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

/* Add floating particles effect */
.app::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(78, 205, 196, 0.1), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 107, 107, 0.1), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(69, 183, 209, 0.1), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(150, 206, 180, 0.1), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: floatingParticles 20s linear infinite;
  z-index: 0;
  pointer-events: none;
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* Add floating particles effect */
.app::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(78, 205, 196, 0.1), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(255, 107, 107, 0.1), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(69, 183, 209, 0.1), transparent),
    radial-gradient(2px 2px at 160px 30px, rgba(150, 206, 180, 0.1), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: floatingParticles 20s linear infinite;
  z-index: 0;
  pointer-events: none;
}

@keyframes floatingParticles {
  0% {
    transform: translateY(0px);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* Background Animation */
.app::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: backgroundShift 8s ease-in-out infinite alternate;
  z-index: 0;
}

@keyframes backgroundShift {
  0% {
    transform: translateX(-10px) translateY(-10px);
  }
  100% {
    transform: translateX(10px) translateY(10px);
  }
}

/* Header Styles */
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(15, 15, 35, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  padding: 1rem 2rem;
  transition: all 0.3s ease;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.logo-icon {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.5));
  animation: logoGlow 3s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  0% {
    filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.5));
  }
  100% {
    filter: drop-shadow(0 0 20px rgba(78, 205, 196, 0.8));
  }
}

.app-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: #e0e0e0;
  letter-spacing: 0.05em;
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: #b0b0b0;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: #4ecdc4;
  background: rgba(78, 205, 196, 0.1);
  transform: translateY(-2px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 80%;
}

/* Main Content */
.main-content {
  text-align: center;
  max-width: 900px;
  position: relative;
  z-index: 1;
  width: 100%;
  margin-top: 80px; /* Account for fixed header */
}

/* Status Bar */
.status-bar {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.2rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.status-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.status-success {
  border-color: rgba(76, 175, 80, 0.3);
  background: rgba(76, 175, 80, 0.1);
}

.status-info {
  border-color: rgba(33, 150, 243, 0.3);
  background: rgba(33, 150, 243, 0.1);
}

.status-icon {
  font-size: 1.2rem;
  animation: pulse 2s ease-in-out infinite;
}

.status-text {
  color: #e0e0e0;
  font-weight: 500;
  font-size: 0.9rem;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Glowing Title */
.glowing-title {
  font-size: clamp(2.5rem, 7vw, 5rem);
  font-weight: 900;
  margin: 0 0 2rem 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientShift 3s ease-in-out infinite, glow 2s ease-in-out infinite alternate;
  text-shadow:
    0 0 20px rgba(255, 107, 107, 0.5),
    0 0 40px rgba(78, 205, 196, 0.3),
    0 0 60px rgba(69, 183, 209, 0.2);
  position: relative;
  letter-spacing: 0.1em;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes glow {
  0% {
    filter: brightness(1) drop-shadow(0 0 20px rgba(255, 107, 107, 0.4));
  }
  100% {
    filter: brightness(1.2) drop-shadow(0 0 30px rgba(78, 205, 196, 0.6));
  }
}

/* Content Section */
.content-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 2.5rem;
  margin-top: 2rem;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.content-section:hover {
  transform: translateY(-5px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Description Text */
.description {
  font-size: 1.2rem;
  line-height: 1.8;
  color: #e0e0e0;
  margin: 0 0 2rem 0;
  text-align: left;
  font-weight: 300;
  letter-spacing: 0.02em;
}

/* Pipeline Concepts Section */
.concepts-section {
  margin-top: 2rem;
  text-align: left;
}

.concepts-title {
  font-size: 1.4rem;
  color: #4ecdc4;
  margin-bottom: 1rem;
  font-weight: 600;
}

.concepts-list {
  list-style: none;
  padding: 0;
}

.concept-item {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 1rem;
  margin-bottom: 0.8rem;
  color: #d0d0d0;
  font-size: 1rem;
  line-height: 1.6;
  transition: all 0.3s ease;
}

.concept-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(78, 205, 196, 0.3);
  transform: translateX(5px);
}

/* Application Information */
.app-info {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.info-label {
  font-weight: 500;
  color: #96ceb4;
  font-size: 0.9rem;
}

.info-value {
  color: #e0e0e0;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
}

/* Footer */
.app-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-text {
  color: #888;
  font-size: 0.9rem;
}

.footer-link {
  color: #4ecdc4;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #45b7d1;
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app {
    padding: 1rem;
  }

  .app-header {
    padding: 0.8rem 1rem;
  }

  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-menu {
    gap: 1rem;
  }

  .main-content {
    margin-top: 120px;
  }

  .status-bar {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .status-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .content-section {
    padding: 1.5rem;
    margin-top: 1.5rem;
  }

  .description {
    font-size: 1rem;
    line-height: 1.6;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
}

@media (max-width: 480px) {
  .content-section {
    padding: 1rem;
  }

  .description {
    font-size: 0.9rem;
    text-align: center;
  }

  .concepts-section {
    text-align: center;
  }

  .concept-item {
    text-align: left;
  }
}
