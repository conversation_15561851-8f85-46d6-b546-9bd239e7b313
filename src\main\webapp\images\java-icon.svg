<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="32" height="32">
  <defs>
    <linearGradient id="javaGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4ecdc4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Java cup shape -->
  <path d="M20 30 Q20 25 25 25 L75 25 Q80 25 80 30 L80 70 Q80 80 70 80 L30 80 Q20 80 20 70 Z" 
        fill="url(#javaGradient)" stroke="#2c3e50" stroke-width="2"/>
  
  <!-- Steam lines -->
  <path d="M35 15 Q35 10 40 15 Q40 10 45 15" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.7"/>
  <path d="M50 15 Q50 10 55 15 Q55 10 60 15" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.7"/>
  <path d="M65 15 Q65 10 70 15 Q70 10 75 15" fill="none" stroke="#4ecdc4" stroke-width="2" opacity="0.7"/>
  
  <!-- Handle -->
  <path d="M80 40 Q90 40 90 50 Q90 60 80 60" fill="none" stroke="#2c3e50" stroke-width="3"/>
  
  <!-- Java text -->
  <text x="50" y="55" font-family="Arial, sans-serif" font-size="12" font-weight="bold" 
        text-anchor="middle" fill="white">J</text>
</svg>
