<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pipeline Testing App - Java Web Application</title>
    <meta name="description" content="A Java web application demonstrating pipeline testing concepts with dynamic visual effects">
    <meta name="keywords" content="java, servlet, jsp, pipeline-testing, web-application">
    <meta name="author" content="Pipeline Testing Demo">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="${pageContext.request.contextPath}/images/java-icon.svg">

    <!-- CSS Styles -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/css/app.css">

    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .app {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .glowing-title {
            text-align: center;
            font-size: 3rem;
            margin: 2rem 0;
            text-shadow: 0 0 20px rgba(255,255,255,0.5);
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255,255,255,0.5); }
            to { text-shadow: 0 0 30px rgba(255,255,255,0.8), 0 0 40px rgba(255,255,255,0.6); }
        }
        .content-section {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            margin: 2rem 0;
        }
        .description {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 2rem;
            text-align: center;
        }
        .app-footer {
            text-align: center;
            margin-top: 3rem;
            padding: 2rem;
        }
        .footer-link {
            color: #fff;
            text-decoration: none;
        }
        .footer-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="app">
        <main class="main-content">
            <!-- Main Title with Glowing Effect -->
            <h1 class="glowing-title">
                Pipeline Testing Hub v1.0
            </h1>

            <!-- Main Content Section -->
            <div class="content-section">
                <p class="description">
                    🚀 Welcome to the Pipeline Testing Hub! This Java web application demonstrates modern DevOps
                    practices and continuous integration concepts. Built with Java Servlets and JSP, it showcases
                    the power of enterprise web technologies combined with robust backend architecture for
                    pipeline automation and deployment workflows.
                </p>
            </div>

            <!-- Footer -->
            <footer class="app-footer">
                <p class="footer-text">
                    Built with Java Servlets & JSP |
                    <a href="${pageContext.request.contextPath}/" class="footer-link">Pipeline Testing Demo</a>
                </p>
            </footer>
        </main>
    </div>
</body>
</html>
