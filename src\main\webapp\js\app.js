/**
 * Pipeline Testing Web Application - JavaScript
 * Provides interactive features and animations for the Java web application
 */

(function() {
    'use strict';

    // Application initialization
    document.addEventListener('DOMContentLoaded', function() {
        initializeApp();
    });

    /**
     * Initialize the application
     */
    function initializeApp() {
        console.log('Pipeline Testing Web Application - Java Version');
        console.log('Application initialized successfully');
        
        // Initialize interactive features
        initializeAnimations();
        initializeInteractions();
        initializeAccessibility();
        
        // Log application info
        logApplicationInfo();
    }

    /**
     * Initialize animations and visual effects
     */
    function initializeAnimations() {
        // Add entrance animation to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            mainContent.style.opacity = '0';
            mainContent.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                mainContent.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
                mainContent.style.opacity = '1';
                mainContent.style.transform = 'translateY(0)';
            }, 100);
        }

        // Add staggered animation to concept items
        const conceptItems = document.querySelectorAll('.concept-item');
        conceptItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 200 + (index * 100));
        });

        // Add animation to info items
        const infoItems = document.querySelectorAll('.info-item');
        infoItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'scale(1)';
            }, 500 + (index * 50));
        });
    }

    /**
     * Initialize interactive features
     */
    function initializeInteractions() {
        // Add click interaction to concept items
        const conceptItems = document.querySelectorAll('.concept-item');
        conceptItems.forEach(item => {
            item.addEventListener('click', function() {
                this.style.transform = 'translateX(10px) scale(1.02)';
                setTimeout(() => {
                    this.style.transform = 'translateX(5px)';
                }, 150);
            });
        });

        // Add hover effects to info items
        const infoItems = document.querySelectorAll('.info-item');
        infoItems.forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(255, 255, 255, 0.08)';
                this.style.borderColor = 'rgba(78, 205, 196, 0.2)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(255, 255, 255, 0.02)';
                this.style.borderColor = 'rgba(255, 255, 255, 0.05)';
            });
        });

        // Add smooth scrolling for any internal links
        const links = document.querySelectorAll('a[href^="#"]');
        links.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    /**
     * Initialize accessibility features
     */
    function initializeAccessibility() {
        // Add keyboard navigation support
        const interactiveElements = document.querySelectorAll('.concept-item, .info-item');
        interactiveElements.forEach(element => {
            // Make elements focusable
            if (!element.hasAttribute('tabindex')) {
                element.setAttribute('tabindex', '0');
            }
            
            // Add keyboard event listeners
            element.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Add focus indicators
        const style = document.createElement('style');
        style.textContent = `
            .concept-item:focus,
            .info-item:focus {
                outline: 2px solid #4ecdc4;
                outline-offset: 2px;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Log application information to console
     */
    function logApplicationInfo() {
        const appInfo = {
            name: 'Pipeline Testing Web Application',
            technology: 'Java Servlets & JSP',
            version: document.querySelector('.info-value') ? 
                     document.querySelector('.info-value').textContent : 'Unknown',
            timestamp: new Date().toISOString()
        };
        
        console.table(appInfo);
    }

    /**
     * Utility function to add CSS class with animation
     */
    function addClassWithAnimation(element, className, delay = 0) {
        setTimeout(() => {
            element.classList.add(className);
        }, delay);
    }

    /**
     * Utility function for smooth transitions
     */
    function smoothTransition(element, properties, duration = 300) {
        element.style.transition = `all ${duration}ms ease`;
        Object.keys(properties).forEach(prop => {
            element.style[prop] = properties[prop];
        });
    }

    // Export utilities for potential external use
    window.PipelineTestingApp = {
        addClassWithAnimation,
        smoothTransition,
        version: '1.0.0'
    };

})();
